import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { calculateCost, logModelUsage, trackModelError } from '@/utils/openai-monitoring';

const apiKey = process.env.OPENAI_API_KEY;
const fallbackApiKey = process.env.OPENAI_FALLBACK_API_KEY;
const preferredModel = process.env.OPENAI_MODEL || "gpt-4.1";
const fallbackModel = process.env.OPENAI_FALLBACK_MODEL || "gpt-4.1-mini";

let openai: OpenAI | null = null;
if (apiKey) {
  openai = new OpenAI({
    apiKey,
    timeout: 60000, // 60 seconds timeout for complex descriptions
    maxRetries: 3, // Enhanced retry logic
  });
} else if (fallbackApiKey) {
  openai = new OpenAI({
    apiKey: fallbackApiKey,
    timeout: 60000,
    maxRetries: 3,
  });
}

interface ProductInfo {
  name: string;
  category?: string;
  features?: string[];
  keywords?: string[];
  targetAudience?: string;
  additionalInfo?: string;
}

interface SeoContent {
  shortDescription: string;
  slug: string;
  wooCommerceMainDescription: string;
  wooCommerceShortDescription: string;
  preservationInfo?: {
    isValid: boolean;
    preservationRate: number;
    missingElements: string[];
    preservedElements: string[];
  };
}

// Função para validar limites de caracteres SEO
const validateSeoLimits = (content: SeoContent): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validar meta description (140-160 caracteres)
  if (content.shortDescription.length < 140 || content.shortDescription.length > 160) {
    errors.push(`Meta description deve ter entre 140-160 caracteres (atual: ${content.shortDescription.length})`);
  }

  // Validar título SEO (50-60 caracteres recomendado para o slug)
  if (content.slug.length > 60) {
    errors.push(`Slug muito longo (atual: ${content.slug.length} caracteres)`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Função CRÍTICA para validar preservação integral de informações adicionais
const validateAdditionalInfoPreservation = (
  content: SeoContent,
  additionalInfo: string
): {
  isValid: boolean;
  errors: string[];
  preservationRate: number;
  missingElements: string[];
  preservedElements: string[];
} => {
  if (!additionalInfo || additionalInfo.trim() === '') {
    return {
      isValid: true,
      errors: [],
      preservationRate: 100,
      missingElements: [],
      preservedElements: []
    };
  }

  const description = content.wooCommerceMainDescription.toLowerCase();

  // Dividir as informações adicionais em elementos-chave mais granulares
  const keyElements = additionalInfo
    .split(/[.!?;,]/) // Incluir vírgulas para capturar mais elementos
    .map(element => element.trim())
    .filter(element => element.length > 3); // Reduzir filtro para capturar mais elementos

  const missingElements: string[] = [];
  const preservedElements: string[] = [];
  let preservedCount = 0;

  // Verificar cada elemento-chave das informações adicionais
  for (const element of keyElements) {
    const elementLower = element.toLowerCase();

    // Extrair palavras-chave significativas, incluindo números e percentagens
    const significantWords = elementLower
      .split(/\s+/)
      .filter(word => {
        // Incluir números, percentagens e palavras técnicas importantes
        if (/\d/.test(word) || word.includes('%')) return true; // Números e percentagens
        if (word.length > 2 && !['para', 'com', 'sem', 'por', 'uma', 'dos', 'das', 'que', 'este', 'esta', 'são', 'ter', 'mais'].includes(word)) return true;
        return false;
      });

    if (significantWords.length === 0) continue;

    // Verificar se as palavras-chave do elemento estão presentes na descrição
    const wordMatches = significantWords.filter(word => description.includes(word));
    const matchRate = wordMatches.length / significantWords.length;

    // Ser mais rigoroso para elementos técnicos importantes
    const hasNumbers = significantWords.some(word => /\d/.test(word) || word.includes('%'));
    const requiredMatchRate = hasNumbers ? 0.8 : 0.6; // 80% para elementos com números, 60% para outros

    if (matchRate >= requiredMatchRate) {
      preservedCount++;
      preservedElements.push(element.trim());
    } else {
      missingElements.push(element.trim());
    }
  }

  const preservationRate = keyElements.length > 0 ? (preservedCount / keyElements.length) * 100 : 100;
  const isValid = preservationRate >= 85; // Aumentado para 85% para ser mais rigoroso

  const errors: string[] = [];
  if (!isValid) {
    errors.push(`⚠️ FALHA CRÍTICA: Taxa de preservação insuficiente: ${preservationRate.toFixed(1)}% (mínimo: 90%)`);
    errors.push(`Elementos em falta: ${missingElements.length}/${keyElements.length}`);
  }

  return {
    isValid,
    errors,
    preservationRate,
    missingElements,
    preservedElements
  };
};

// Função para normalizar o slug
const generateSlug = (text: string): string => {
  const a = 'àáâäæãåāăąçćčđďèéêëēėęěğǵḧîïíīįìłḿñńǹňôöòóœøōõőṕŕřßśšşșťțûüùúūǘůűųẃẍÿýžźż·/_,:;';
  const b = 'aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrssssssttuuuuuuuuuwxyyzzz------';
  const p = new RegExp(a.split('').join('|'), 'g');

  return text.toString().toLowerCase()
    .replace(/\s+/g, '-') // Substituir espaços por -
    .replace(p, c => b.charAt(a.indexOf(c))) // Substituir caracteres especiais
    .replace(/&/g, '-and-') // Substituir & por '-and-'
    .replace(/[^\w\-]+/g, '') // Remover caracteres inválidos
    .replace(/\-\-+/g, '-') // Substituir múltiplos - por um único -
    .replace(/^-+/, '') // Remover - do início
    .replace(/-+$/, ''); // Remover - do fim
};

// Função para limpeza de formatação indesejada
const cleanUnwantedFormatting = (text: string): string => {
  if (!text) return '';

  let cleaned = text;

  // Remove caracteres de controle e caracteres invisíveis
  cleaned = cleaned.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

  // Remove aspas tipográficas problemáticas e substitui por aspas normais
  cleaned = cleaned.replace(/[""]/g, '"');
  cleaned = cleaned.replace(/['']/g, "'");

  // Remove formatação de sublinhado
  cleaned = cleaned.replace(/<u[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/u>/gi, '');

  // Remove formatação de itálico
  cleaned = cleaned.replace(/<em[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/em>/gi, '');
  cleaned = cleaned.replace(/<i[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/i>/gi, '');

  // Remove markdown de itálico
  cleaned = cleaned.replace(/\*([^*]+)\*/g, '$1');
  cleaned = cleaned.replace(/_([^_]+)_/g, '$1');

  // Remove qualquer formatação de sublinhado em markdown
  cleaned = cleaned.replace(/__([^_]+)__/g, '**$1**');

  // Converte markdown bold para HTML strong (se necessário)
  cleaned = cleaned.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

  // Remove espaços extras e normaliza espaçamento
  cleaned = cleaned.replace(/\s+/g, ' ');
  cleaned = cleaned.trim();

  return cleaned;
};

// Função para correção ortográfica automática pós-geração
const correctPortugueseText = (text: string): string => {
  if (!text) return '';

  let corrected = text;

  // Correções ortográficas específicas do português de Portugal
  const corrections: { [key: string]: string } = {
    // Erros de acentuação comuns
    'facil': 'fácil',
    'util': 'útil',
    'pratico': 'prático',
    'economico': 'económico',
    'tecnologico': 'tecnológico',
    'automatico': 'automático',
    'ergonomico': 'ergonómico',
    'comodo': 'cómodo',
    'rapido': 'rápido',
    'solido': 'sólido',
    'versatil': 'versátil',
    'portatil': 'portátil',
    'flexivel': 'flexível',

    // Correções de ortografia
    'qualidadde': 'qualidade',
    'funcionalidadde': 'funcionalidade',
    'resistênte': 'resistente',
    'duravél': 'durável',
    'electrónico': 'eletrónico',
    'electrónicos': 'eletrónicos',
    'electrónica': 'eletrónica',
    'optimizar': 'otimizar',
    'optimizado': 'otimizado',
    'optimização': 'otimização',

    // Termos brasileiros para portugueses
    'celular': 'telemóvel',
    'mouse': 'rato',
    'deletar': 'eliminar',
    'salvar': 'guardar',
    'tela': 'ecrã',
    'aplicativo': 'aplicação',
    'usuário': 'utilizador',
    'usuários': 'utilizadores',

    // Correções de concordância comum
    'uma produto': 'um produto',
    'esta produto': 'este produto',
    'essa produto': 'esse produto',
    'o qualidade': 'a qualidade',
    'este qualidade': 'esta qualidade',

    // Expressões problemáticas
    'ajustável de alta qualidade': 'ajustável e de alta qualidade',
    'resistente de qualidade': 'resistente e de qualidade',
    'ideal para quem busca': 'ideal para quem procura',
    'perfeito para quem quer': 'perfeito para quem deseja'
  };

  // Aplicar correções
  Object.entries(corrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  // Remover títulos de secções indesejados
  corrected = corrected.replace(/<strong>\s*Características principais:\s*<\/strong>/gi, '');
  corrected = corrected.replace(/\*\*Características principais:\*\*/gi, '');
  corrected = corrected.replace(/Características principais:/gi, '');
  corrected = corrected.replace(/<strong>\s*Características:\s*<\/strong>/gi, '');
  corrected = corrected.replace(/\*\*Características:\*\*/gi, '');
  corrected = corrected.replace(/Características:/gi, '');

  // Corrigir espaçamento
  corrected = corrected.replace(/\s+/g, ' ');
  corrected = corrected.replace(/\s+([.,;:!?])/g, '$1');
  corrected = corrected.replace(/([.,;:!?])([a-zA-Z])/g, '$1 $2');

  // Capitalização após pontuação
  corrected = corrected.replace(/([.!?])\s+([a-z])/g, (_, punct, letter) => {
    return punct + ' ' + letter.toUpperCase();
  });

  return corrected.trim();
};

// Simple utility functions for AI-generated content











export async function POST(request: Request) {

  if (!openai) {
    return NextResponse.json(
      { error: 'A API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY.' },
      { status: 500 }
    );
  }

  try {
    const body = await request.json();
    let seoContent: SeoContent;

    if (body.action === 'generate') {
      seoContent = await generateSeoContent(body.productInfo);
    } else if (body.action === 'improve') {
      seoContent = await improveSeoContent(body.currentDescription, body.productInfo?.name);
    } else {
      return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
    }

    return NextResponse.json({ seoContent });

  } catch (error) {
    console.error('Erro na API de descrição de produto:', error);

    // Handle specific OpenAI API errors
    if (error && typeof error === 'object' && 'status' in error) {
      const openaiError = error as any;

      switch (openaiError.status) {
        case 429:
          return NextResponse.json({
            error: 'Quota da API OpenAI excedida. Por favor, verifique o seu plano e detalhes de faturação.'
          }, { status: 429 });

        case 401:
          return NextResponse.json({
            error: 'Chave da API OpenAI inválida. Por favor, verifique a configuração.'
          }, { status: 401 });

        case 400:
          return NextResponse.json({
            error: 'Pedido inválido enviado para a API OpenAI. Por favor, tente novamente.'
          }, { status: 400 });

        case 503:
          return NextResponse.json({
            error: 'Serviço OpenAI temporariamente indisponível. Tente novamente em alguns minutos.'
          }, { status: 503 });

        default:
          return NextResponse.json({
            error: `Erro da API OpenAI (${openaiError.status}): ${openaiError.message || 'Erro desconhecido'}`
          }, { status: openaiError.status || 500 });
      }
    }

    // Handle other types of errors
    if (error instanceof Error) {
      if (error.message.includes('API da OpenAI não configurada')) {
        return NextResponse.json({
          error: 'API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY.'
        }, { status: 500 });
      }

      if (error.message.includes('JSON válido')) {
        return NextResponse.json({
          error: 'Resposta inválida da API OpenAI. Tente novamente.'
        }, { status: 500 });
      }

      return NextResponse.json({
        error: `Erro: ${error.message}`
      }, { status: 500 });
    }

    // Fallback for unknown errors
    return NextResponse.json({
      error: 'Falha ao processar o pedido. Tente novamente.'
    }, { status: 500 });
  }
}

async function generateSeoContent(productInfo: ProductInfo): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Analise as seguintes informações de um produto para uma loja WooCommerce em Portugal:
    - Nome do Produto: ${productInfo.name}
    ${productInfo.category ? `- Categoria: ${productInfo.category}` : ''}
    ${productInfo.features && productInfo.features.length > 0 ? `- Características: ${productInfo.features.join(', ')}` : ''}
    ${productInfo.keywords && productInfo.keywords.length > 0 ? `- Palavras-chave para SEO (OBRIGATÓRIO INCORPORAR): ${productInfo.keywords.join(', ')}` : ''}
    ${productInfo.targetAudience ? `- Público-alvo: ${productInfo.targetAudience}` : ''}
    ${productInfo.additionalInfo ? `- Informações Adicionais: ${productInfo.additionalInfo}` : ''}

    **INSTRUÇÕES CRÍTICAS DE QUALIDADE E DIVERSIDADE:**
    1. Use toda a sua inteligência artificial para criar conteúdo ÚNICO, natural e persuasivo
    2. NÃO use templates ou frases genéricas - seja criativo e específico para este produto
    3. ${productInfo.keywords && productInfo.keywords.length > 0 ? `OBRIGATÓRIO: Incorpore naturalmente as palavras-chave SEO fornecidas nas descrições` : ''}
    4. Crie conteúdo que soe humano, não robótico
    5. Foque nos benefícios únicos deste produto específico

    **INSTRUÇÕES AVANÇADAS DE DIVERSIDADE:**
    6. VARIE DRASTICAMENTE a estrutura das frases: use frases curtas, médias e longas
    7. ALTERNE entre diferentes estilos narrativos: descritivo, persuasivo, técnico, emocional
    8. USE DIFERENTES PERSPECTIVAS: segunda pessoa ("você"), terceira pessoa, voz passiva, voz ativa
    9. VARIE O RITMO: combine frases dinâmicas com pausas reflexivas
    10. EXPLORE DIFERENTES ÂNGULOS: funcionalidade, estética, experiência, valor, inovação
    11. NUNCA repita estruturas de frases ou padrões de escrita de descrições anteriores
    12. CRIE VARIAÇÕES SINTÁTICAS: inverta ordem de palavras, use diferentes conectores
    13. ALTERNE REGISTOS: formal/informal, técnico/acessível, direto/elaborado (sempre apropriado ao produto)

    **INSTRUÇÕES CRÍTICAS PARA PRESERVAÇÃO INTEGRAL DE INFORMAÇÕES DO UTILIZADOR:**
    ${productInfo.additionalInfo ? `
    14. ⚠️ REQUISITO CRÍTICO ABSOLUTO ⚠️: As seguintes informações adicionais fornecidas pelo utilizador DEVEM ser preservadas a 100% na descrição:
        "${productInfo.additionalInfo}"

    15. METODOLOGIA OBRIGATÓRIA DE PRESERVAÇÃO:
        - LEIA cada frase das informações adicionais individualmente
        - IDENTIFIQUE todos os elementos: nomes completos, percentagens, tecnologias, benefícios, composições, ingredientes, especificações
        - CRIE uma secção dedicada na descrição para cada tipo de informação (composição nutricional, tecnologia, benefícios, especificações técnicas)
        - INTEGRE cada elemento específico com o seu valor/percentagem/detalhe exato
        - USE terminologia técnica precisa quando fornecida

    16. ESTRUTURA OBRIGATÓRIA PARA INFORMAÇÕES TÉCNICAS:
        - Se houver composição nutricional → Crie secção "Composição Nutricional" com todos os valores
        - Se houver ingredientes → Liste todos os ingredientes com percentagens exatas
        - Se houver tecnologia/fabrico → Destaque todos os processos e tecnologias mencionados
        - Se houver benefícios específicos → Enumere todos os benefícios com detalhes técnicos
        - Se houver especificações → Inclua todas as especificações técnicas com valores precisos

    17. VALIDAÇÃO OBRIGATÓRIA ANTES DE FINALIZAR:
        - CONTE quantos elementos das informações adicionais incluiu
        - VERIFIQUE se cada nome de produto, percentagem, tecnologia e benefício está presente
        - CONFIRME que nenhuma informação técnica foi omitida ou simplificada
        - GARANTA que a terminologia técnica original foi mantida

    18. EXEMPLO DE INTEGRAÇÃO CORRETA:
        Se informações adicionais contêm "Rico em proteína (32%), com tecnologia de extrusão dupla"
        → DEVE aparecer na descrição: "Rico em proteína (32%)" E "tecnologia de extrusão dupla"
        → NUNCA apenas "rico em proteína" ou "tecnologia avançada"
    ` : '19. Nenhuma informação adicional fornecida pelo utilizador.'}

    **INSTRUÇÕES CRÍTICAS PARA ABERTURAS DINÂMICAS:**
    6. OBRIGATÓRIO: NUNCA use sempre a mesma palavra de abertura como "Descubra"
    7. OBRIGATÓRIO: ANALISE PRIMEIRO o produto para determinar o tipo correto antes de escolher a abertura:

       **PASSO 1 - ANÁLISE DO PRODUTO:**
       - Leia cuidadosamente o nome, categoria, características e informações adicionais
       - Determine se é: consumível vs não-consumível, para humanos vs animais vs uso geral
       - Identifique a função principal: alimentação, tecnologia, decoração, vestuário, cuidados, etc.

       **PASSO 2 - VALIDAÇÃO DE SEGURANÇA:**
       - NUNCA use aberturas alimentares ("Prove", "Delicie-se", "Saboreie") para produtos NÃO-ALIMENTARES
       - NUNCA use "Prove" ou "Saboreie" para produtos de animais, tecnologia, casa, roupa, etc.
       - NUNCA use aberturas de bem-estar humano para produtos de animais

       **PASSO 3 - SELEÇÃO CONTEXTUAL:**

       **PRODUTOS ALIMENTARES HUMANOS** (comida, bebidas, suplementos para humanos):
       → "Saboreie", "Delicie-se com", "Prove", "Experimente o sabor"

       **PRODUTOS PARA ANIMAIS** (ração, brinquedos, acessórios para pets):
       → "Cuide do seu", "Proporcione ao seu", "Ofereça ao seu", "Mime o seu"

       **TECNOLOGIA/ELECTRÓNICOS** (smartphones, computadores, gadgets):
       → "Experimente", "Explore", "Conheça", "Aproveite a tecnologia"

       **CASA/DECORAÇÃO/MOBILIÁRIO** (móveis, decoração, utensílios):
       → "Transforme", "Renove", "Decore", "Organize", "Crie"

       **MODA/VESTUÁRIO/ACESSÓRIOS** (roupa, sapatos, jóias, relógios):
       → "Vista", "Combine", "Destaque-se com", "Eleve o seu estilo"

       **BEM-ESTAR/SAÚDE HUMANA** (cosméticos, cuidados pessoais, fitness):
       → "Cuide de si com", "Mime-se com", "Sinta", "Desfrute"

       **PROFISSIONAL/ESCRITÓRIO** (material de escritório, ferramentas de trabalho):
       → "Optimize", "Melhore", "Potencialize", "Organize"

       **LAZER/HOBBY/DESPORTO** (jogos, instrumentos, equipamento desportivo):
       → "Divirta-se com", "Pratique com", "Explore", "Aventure-se"

       **PRODUTOS NEUTROS/GERAIS** (quando não se enquadra claramente):
       → "Conheça", "Aproveite", "Escolha", "Adquira", ou começar directamente com o nome do produto

    8. OBRIGATÓRIO: Se tiver dúvidas sobre o tipo de produto, use aberturas neutras seguras
    9. OBRIGATÓRIO: A abertura deve fazer sentido lógico para o produto específico
    10. OBRIGATÓRIO: Varie entre diferentes estilos mas sempre apropriados ao contexto

    **INSTRUÇÕES CRÍTICAS DE ORTOGRAFIA E GRAMÁTICA:**
    11. OBRIGATÓRIO: Use exclusivamente português de Portugal com ortografia 100% correta
    12. OBRIGATÓRIO: Aplique concordância de género e número perfeita (o/a, os/as)
    13. OBRIGATÓRIO: Use acentuação correta em todas as palavras (à, é, ó, etc.)
    14. OBRIGATÓRIO: Evite brasileirismos - use apenas termos portugueses
    15. OBRIGATÓRIO: Revise cada palavra para garantir ortografia impecável
    16. OBRIGATÓRIO: Use pontuação correta e estrutura gramatical adequada

    **INSTRUÇÕES CRÍTICAS DE FORMATAÇÃO HTML:**
    17. OBRIGATÓRIO: Use APENAS formatação em negrito com tags <strong> para destacar elementos importantes
    18. OBRIGATÓRIO: NUNCA use sublinhado (<u> tags), itálico (<em> tags), ou qualquer outra formatação
    19. OBRIGATÓRIO: NÃO inclua cabeçalhos como "Características principais:" ou similares
    20. OBRIGATÓRIO: Apresente as características diretamente em parágrafos ou texto corrido
    21. OBRIGATÓRIO: Evite títulos de secções desnecessários nas descrições
    22. OBRIGATÓRIO: Use <strong> inteligentemente para destacar:
        - Especificações técnicas importantes (percentagens, medidas, capacidades)
        - Nomes de tecnologias e processos
        - Benefícios-chave únicos do produto
        - Características distintivas
        - Nomes de marcas ou certificações
    23. OBRIGATÓRIO: Mantenha formatação HTML limpa e compatível com WooCommerce

    Com base nisso, gere o seguinte conteúdo em português de Portugal, seguindo estritamente as regras para cada campo. A resposta DEVE ser um objeto JSON válido.

    **IMPORTANTE:** A curta descrição deve ser uma versão resumida e específica da descrição principal, não um texto genérico. Extraia as características mais importantes da descrição completa.

    **CRÍTICO - LIMITE SEO:** A descrição SEO (shortDescription) DEVE ter MÁXIMO 160 caracteres. CONTE OS CARACTERES. Se passar de 160, REESCREVA até ficar dentro do limite. Seja específico sobre o produto, não genérico.

    1.  **wooCommerceMainDescription (Descrição WooCommerce):**
        -   **Objetivo:** Ser a descrição COMPLETA e ABRANGENTE do produto no WooCommerce. Deve ser rica em detalhes, otimizada para conversão e SEO.
        -   **TAMANHO:** SEM LIMITES de caracteres - crie descrições tão detalhadas quanto necessário para apresentar completamente o produto
        -   **Estilo:** Use formatação HTML estruturada para melhor apresentação visual e experiência do utilizador
        -   **Tom:** Abrangente, informativo, persuasivo e envolvente - adapte ao produto específico
        -   **ESTRUTURA AVANÇADA OBRIGATÓRIA:**
            1. **Parágrafo de abertura dinâmico** (com abertura contextual apropriada)
            2. **Secção de características principais** (cada característica num parágrafo separado com <strong>)
            3. **Secção de benefícios e valor** (como o produto melhora a vida do utilizador)
            ${productInfo.additionalInfo ? `4. **SECÇÃO OBRIGATÓRIA DE INFORMAÇÕES TÉCNICAS DETALHADAS** (TODAS as informações adicionais organizadas por categoria):
               - Composição/Ingredientes (se aplicável): Todos os componentes com percentagens exatas
               - Especificações Técnicas (se aplicável): Todas as especificações com valores precisos
               - Tecnologia/Fabrico (se aplicável): Todos os processos e tecnologias mencionados
               - Benefícios Específicos (se aplicável): Todos os benefícios com detalhes técnicos
               - Certificações/Aprovações (se aplicável): Todas as certificações e aprovações mencionadas
            5. **Secção de casos de uso/aplicações** (como e quando usar)
            6. **Parágrafo de fecho persuasivo** (call-to-action subtil)` : `4. **Secção técnica/especificações** (quando relevante)
            5. **Secção de casos de uso/aplicações** (como e quando usar)
            6. **Parágrafo de fecho persuasivo** (call-to-action subtil)`}
        -   **FORMATAÇÃO HTML OBRIGATÓRIA:**
            - Use APENAS <strong> tags para destacar elementos importantes
            - NUNCA use <u> (sublinhado), <em> (itálico) ou outras formatações
            - Aplique <strong> inteligentemente em:
              * Especificações técnicas (percentagens, capacidades, medidas)
              * Nomes de tecnologias e processos de fabrico
              * Benefícios-chave únicos e diferenciais competitivos
              * Certificações, prémios e aprovações
              * Características distintivas do produto
            - Varie o comprimento dos parágrafos para criar ritmo visual
            - Mantenha HTML limpo e compatível com WooCommerce
        -   **CONTEÚDO OBRIGATÓRIO:**
            ${productInfo.additionalInfo ? `
            - INTEGRE OBRIGATORIAMENTE CADA ELEMENTO DAS INFORMAÇÕES ADICIONAIS: "${productInfo.additionalInfo}"
            - MANTENHA terminologia técnica exata (percentagens, nomes científicos, especificações)
            - ORGANIZE as informações técnicas em secções claras e estruturadas
            - DESTAQUE cada componente, benefício e especificação individualmente
            - PRESERVE todos os valores numéricos, percentagens e medidas exatas
            - INCLUA todos os nomes de tecnologias, processos e metodologias mencionados` : ''}
            - Todas as características fornecidas pelo utilizador
            - Benefícios específicos e casos de uso
            - Informações técnicas relevantes
            - Proposta de valor única do produto

    2.  **wooCommerceShortDescription (Curta Descrição WooCommerce):**
        -   **Objetivo:** Ser uma versão resumida e específica da descrição principal em formato de texto corrido.
        -   **Estilo:** Um parágrafo conciso que sintetiza os pontos mais importantes da descrição completa.
        -   **Tom:** Direto, específico e focado nos benefícios únicos deste produto em particular.
        -   **Formato:** Texto corrido em parágrafo HTML simples usando tag <p>, SEM listas.
        -   **Formatação:** Use APENAS <strong> tags para destacar 1-2 elementos-chave. NUNCA use <u> ou <em>.
        -   **Conteúdo:** Deve resumir as principais características e benefícios mencionados na descrição completa.
        -   **Importante:** NÃO use frases genéricas. Seja específico sobre este produto e suas características únicas.
        -   **Tamanho:** 2-3 frases que capturem a essência do produto e seus principais benefícios.
        -   **Exemplo de formatação correta:** <p>Este produto oferece <strong>tecnologia avançada</strong> com características únicas para máximo desempenho.</p>

    3.  **shortDescription (Descrição SEO):**
        -   **Objetivo:** Meta description inteligente e específica que identifique exatamente o que é o produto e seus benefícios únicos.
        -   **REGRA ABSOLUTA:** MÁXIMO 160 caracteres (incluindo espaços). Conte cada caractere. Se passar de 160, REESCREVA até ficar dentro do limite.
        -   **Conteúdo OBRIGATÓRIO:** Deve incluir:
            1. O QUE é o produto (tipo específico, não genérico)
            2. PRINCIPAL característica/benefício único
            3. PARA QUEM é destinado (se relevante)
        -   **Estilo:** Específico, direto, sem palavras vazias como "alta qualidade", "excelente", "premium" (a menos que seja realmente diferencial)
        -   **Exemplos de RUIM (genéricos):**
            * "Produto de alta qualidade com excelente design e materiais premium"
            * "Artigo moderno e funcional para uso diário"
        -   **Exemplos de BOM (específicos):**
            * "Sapatos de couro italiano com palmilha gel anti-impacto para profissionais" (79 chars)
            * "Smartphone Android 12GB RAM, câmara 108MP, bateria 5000mAh" (60 chars)
            * "Mesa escritório madeira maciça 120x60cm com gavetas organizadoras" (67 chars)
        -   **FÓRMULA:** [TIPO PRODUTO] + [MATERIAL/TECNOLOGIA] + [BENEFÍCIO PRINCIPAL] + [PÚBLICO-ALVO se relevante]
        -   **VERIFICAÇÃO FINAL:** Conte os caracteres. Se > 160, corte palavras até ficar ≤ 160.

    4.  **slug (Slug):**
        -   **Objetivo:** Criar um URL amigável para SEO.
        -   **Estilo:** Use o nome do produto como base. Converta para minúsculas, substitua espaços por hífens e remova "stop words" (ex: de, para, o, a) e caracteres especiais.
        -   **Exemplo:** Se o nome for "Sapatos de Couro para Homem", o slug deve ser "sapatos-couro-homem".

    Responda APENAS com o objeto JSON, formatado da seguinte maneira:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  try {
    console.log("🚀 Iniciando chamada à API OpenAI para geração de conteúdo...");
    console.log("📝 Tamanho do prompt:", prompt.length, "caracteres");
    console.log("🤖 Modelo selecionado:", preferredModel);

    const startTime = Date.now();
    let response;
    let modelUsed = preferredModel;

    try {
      response = await openai.chat.completions.create({
      model: preferredModel, // Dynamic model selection from environment
      messages: [
        {
          role: "system",
          content: "Você é um especialista técnico em SEO e copywriting para e-commerce português, especializado em preservar e integrar INTEGRALMENTE todas as informações técnicas fornecidas. Sua especialidade é produzir texto em português de Portugal com ortografia impecável, garantindo que NENHUMA informação técnica, percentagem, especificação ou detalhe seja omitido. CRÍTICO: Você DEVE incluir TODOS os elementos das informações adicionais na descrição final, organizando-os em secções técnicas claras. NUNCA simplifique ou omita dados técnicos, percentagens, nomes de tecnologias ou especificações. FORMATAÇÃO OBRIGATÓRIA: Use APENAS formatação em negrito (<strong> tags) para destacar elementos importantes como características técnicas, benefícios-chave, especificações e nomes de marcas. NUNCA use sublinhado (<u> tags), itálico (<em> tags) ou qualquer outra formatação. Sua tarefa é gerar conteúdo WooCommerce tecnicamente completo e preciso, mantendo português europeu perfeito com formatação HTML limpa."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7, // Optimal for creative yet consistent content
      max_tokens: 2500, // Increased for more detailed descriptions with 1M context
      response_format: { type: "json_object" },
      top_p: 0.9, // Balanced for quality and diversity
      frequency_penalty: 0.4, // Prevents repetition
      presence_penalty: 0.3, // Encourages topic diversity
    });
    } catch (modelError: any) {
      // Track primary model failure
      trackModelError(preferredModel, modelError, 'generateSeoContent');

      // Fallback to cheaper model if primary model fails
      console.warn(`⚠️ Modelo ${preferredModel} falhou, tentando fallback para ${fallbackModel}:`, modelError.message);
      modelUsed = fallbackModel;

      response = await openai.chat.completions.create({
        model: fallbackModel,
        messages: [
          {
            role: "system",
            content: "Você é um especialista técnico em SEO e copywriting para e-commerce português, especializado em preservar e integrar INTEGRALMENTE todas as informações técnicas fornecidas. Sua especialidade é produzir texto em português de Portugal com ortografia impecável, garantindo que NENHUMA informação técnica, percentagem, especificação ou detalhe seja omitido. CRÍTICO: Você DEVE incluir TODOS os elementos das informações adicionais na descrição final, organizando-os em secções técnicas claras. NUNCA simplifique ou omita dados técnicos, percentagens, nomes de tecnologias ou especificações. FORMATAÇÃO OBRIGATÓRIA: Use APENAS formatação em negrito (<strong> tags) para destacar elementos importantes como características técnicas, benefícios-chave, especificações e nomes de marcas. NUNCA use sublinhado (<u> tags), itálico (<em> tags) ou qualquer outra formatação. Sua tarefa é gerar conteúdo WooCommerce tecnicamente completo e preciso, mantendo português europeu perfeito com formatação HTML limpa."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2500,
        response_format: { type: "json_object" },
        top_p: 0.9,
        frequency_penalty: 0.4,
        presence_penalty: 0.3,
      });
      console.log("✅ Resposta recebida da API OpenAI (modelo fallback)");
    }

    // Log performance metrics
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (response.usage) {
      const cost = calculateCost(
        modelUsed,
        response.usage.prompt_tokens,
        response.usage.completion_tokens
      );

      logModelUsage({
        model: modelUsed,
        timestamp: startTime,
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
        cost,
        responseTime,
        success: true,
      });
    }

    console.log("✅ Resposta recebida da API OpenAI");

    const content = response.choices[0].message.content;
    if (!content) {
      console.error("❌ Resposta da API OpenAI está vazia");
      throw new Error("A resposta da API está vazia.");
    }

    console.log("📄 Conteúdo recebido da API (primeiros 200 chars):", content.substring(0, 200));

    try {
      const parsedContent: SeoContent = JSON.parse(content);
      console.log("✅ JSON parseado com sucesso");

      // Debug: Log original content before processing
      console.log("🔍 Original shortDescription:", JSON.stringify(parsedContent.shortDescription));

      // Aplicar limpeza de formatação e correção ortográfica automática a todos os campos de texto
      parsedContent.wooCommerceMainDescription = correctPortugueseText(cleanUnwantedFormatting(parsedContent.wooCommerceMainDescription));
      parsedContent.wooCommerceShortDescription = correctPortugueseText(cleanUnwantedFormatting(parsedContent.wooCommerceShortDescription));
      parsedContent.shortDescription = correctPortugueseText(cleanUnwantedFormatting(parsedContent.shortDescription));

      // Debug: Log processed content
      console.log("🔍 Processed shortDescription:", JSON.stringify(parsedContent.shortDescription));

      // Garante que o slug está bem formatado, mesmo que a IA falhe
      parsedContent.slug = generateSlug(parsedContent.slug || productInfo.name);

      // VALIDAÇÃO CRÍTICA: Preservação de informações adicionais
      let preservationValidation = null;
      if (productInfo.additionalInfo && productInfo.additionalInfo.trim() !== '') {
        preservationValidation = validateAdditionalInfoPreservation(parsedContent, productInfo.additionalInfo);

        if (!preservationValidation.isValid) {
          console.error("⚠️ AVISO: Informações adicionais não totalmente preservadas:", preservationValidation.errors);
          console.error("Taxa de preservação:", preservationValidation.preservationRate.toFixed(1) + "%");
          console.error("Elementos em falta:", preservationValidation.missingElements);

          // Log detalhado mas continuar com a resposta (não falhar)
          console.warn("Continuando com a resposta apesar da preservação incompleta...");
        } else {
          console.log("✅ Informações adicionais preservadas com sucesso:", preservationValidation.preservationRate.toFixed(1) + "%");
          console.log("Elementos preservados:", preservationValidation.preservedElements.length);
        }

        // Adicionar informações de preservação ao conteúdo retornado
        parsedContent.preservationInfo = {
          isValid: preservationValidation.isValid,
          preservationRate: preservationValidation.preservationRate,
          missingElements: preservationValidation.missingElements,
          preservedElements: preservationValidation.preservedElements
        };
      }

      // Validar limites de SEO
      const validation = validateSeoLimits(parsedContent);
      if (!validation.isValid) {
        console.warn("Limites de SEO não respeitados:", validation.errors);

        // Tentar corrigir a meta description se estiver fora dos limites
        if (parsedContent.shortDescription.length > 160) {
          console.log(`🔧 Truncando meta description: ${parsedContent.shortDescription.length} -> 160 caracteres`);

          // Se muito longa, truncar de forma inteligente
          let truncated = parsedContent.shortDescription.substring(0, 157);

          // Tentar cortar na última palavra completa
          const lastSpace = truncated.lastIndexOf(' ');
          if (lastSpace > 140) {
            truncated = truncated.substring(0, lastSpace);
          }

          // Limpar caracteres especiais no final antes de adicionar reticências
          truncated = truncated.replace(/[.,;:!?\s]+$/, '');

          // Adicionar reticências se necessário
          if (truncated.length < parsedContent.shortDescription.length) {
            truncated += "...";
          }

          // Garantir que não excede 160 caracteres
          if (truncated.length > 160) {
            truncated = truncated.substring(0, 157) + "...";
          }

          parsedContent.shortDescription = truncated;
          console.log(`✅ Meta description corrigida: ${parsedContent.shortDescription.length} caracteres`);
          console.log(`📝 Resultado: "${parsedContent.shortDescription}"`);
        }
      }

      return parsedContent;
    } catch (e) {
      console.error("❌ Falha ao analisar JSON da API:");
      console.error("Conteúdo recebido:", content);
      console.error("Erro de parsing:", e);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Re-throw OpenAI API errors to be handled by the main catch block
    throw error;
  }
}

async function improveSeoContent(currentDescription: string, productName?: string): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Analise a seguinte descrição de produto (e o seu nome, se disponível) de uma loja WooCommerce em Portugal:
    - Nome do Produto: ${productName || 'Não fornecido'}
    - Descrição Atual: "${currentDescription}"

    **INSTRUÇÕES AVANÇADAS DE DIVERSIDADE E QUALIDADE:**
    1. CRIE conteúdo COMPLETAMENTE ÚNICO em estrutura, vocabulário e abordagem
    2. VARIE DRASTICAMENTE: comprimento de frases, perspectivas narrativas, registos linguísticos
    3. NUNCA repita padrões sintáticos ou estruturas de frases de descrições anteriores
    4. ALTERNE entre estilos: descritivo, persuasivo, técnico, emocional
    5. USE diferentes ângulos: funcionalidade, estética, experiência, valor, inovação
    6. PRESERVE INTEGRALMENTE todas as informações técnicas da descrição original

    **INSTRUÇÕES CRÍTICAS PARA PRESERVAÇÃO TÉCNICA COMPLETA:**
    7. IDENTIFIQUE todos os dados técnicos na descrição original: percentagens, especificações, tecnologias, benefícios
    8. MANTENHA todos os valores numéricos, percentagens e medidas exatas
    9. PRESERVE toda a terminologia técnica e científica original
    10. ORGANIZE as informações técnicas em secções claras e estruturadas
    11. NUNCA simplifique ou omita dados técnicos durante a reescrita

    **INSTRUÇÕES CRÍTICAS PARA ABERTURAS DINÂMICAS:**
    7. OBRIGATÓRIO: NUNCA use sempre a mesma palavra de abertura como "Descubra"
    8. OBRIGATÓRIO: ANALISE PRIMEIRO o produto para determinar o tipo correto antes de escolher a abertura:

       **PASSO 1 - ANÁLISE DO PRODUTO:**
       - Leia cuidadosamente o nome e descrição atual
       - Determine se é: consumível vs não-consumível, para humanos vs animais vs uso geral
       - Identifique a função principal: alimentação, tecnologia, decoração, vestuário, cuidados, etc.

       **PASSO 2 - VALIDAÇÃO DE SEGURANÇA:**
       - NUNCA use aberturas alimentares ("Prove", "Delicie-se", "Saboreie") para produtos NÃO-ALIMENTARES
       - NUNCA use "Prove" ou "Saboreie" para produtos de animais, tecnologia, casa, roupa, etc.
       - NUNCA use aberturas de bem-estar humano para produtos de animais

       **PASSO 3 - SELEÇÃO CONTEXTUAL:**

       **PRODUTOS ALIMENTARES HUMANOS** (comida, bebidas, suplementos para humanos):
       → "Saboreie", "Delicie-se com", "Prove", "Experimente o sabor"

       **PRODUTOS PARA ANIMAIS** (ração, brinquedos, acessórios para pets):
       → "Cuide do seu", "Proporcione ao seu", "Ofereça ao seu", "Mime o seu"

       **TECNOLOGIA/ELECTRÓNICOS** (smartphones, computadores, gadgets):
       → "Experimente", "Explore", "Conheça", "Aproveite a tecnologia"

       **CASA/DECORAÇÃO/MOBILIÁRIO** (móveis, decoração, utensílios):
       → "Transforme", "Renove", "Decore", "Organize", "Crie"

       **MODA/VESTUÁRIO/ACESSÓRIOS** (roupa, sapatos, jóias, relógios):
       → "Vista", "Combine", "Destaque-se com", "Eleve o seu estilo"

       **BEM-ESTAR/SAÚDE HUMANA** (cosméticos, cuidados pessoais, fitness):
       → "Cuide de si com", "Mime-se com", "Sinta", "Desfrute"

       **PROFISSIONAL/ESCRITÓRIO** (material de escritório, ferramentas de trabalho):
       → "Optimize", "Melhore", "Potencialize", "Organize"

       **LAZER/HOBBY/DESPORTO** (jogos, instrumentos, equipamento desportivo):
       → "Divirta-se com", "Pratique com", "Explore", "Aventure-se"

       **PRODUTOS NEUTROS/GERAIS** (quando não se enquadra claramente):
       → "Conheça", "Aproveite", "Escolha", "Adquira", ou começar directamente com o nome do produto

    3. OBRIGATÓRIO: Se tiver dúvidas sobre o tipo de produto, use aberturas neutras seguras
    4. OBRIGATÓRIO: A abertura deve fazer sentido lógico para o produto específico
    5. OBRIGATÓRIO: Varie entre diferentes estilos mas sempre apropriados ao contexto

    **INSTRUÇÕES CRÍTICAS DE ORTOGRAFIA E GRAMÁTICA:**
    6. OBRIGATÓRIO: Corrija automaticamente todos os erros ortográficos
    7. OBRIGATÓRIO: Use exclusivamente português de Portugal com ortografia 100% correta
    8. OBRIGATÓRIO: Aplique concordância de género e número perfeita (o/a, os/as)
    9. OBRIGATÓRIO: Use acentuação correta em todas as palavras (à, é, ó, etc.)
    10. OBRIGATÓRIO: Evite brasileirismos - use apenas termos portugueses
    11. OBRIGATÓRIO: Revise cada palavra para garantir ortografia impecável

    **INSTRUÇÕES CRÍTICAS DE FORMATAÇÃO HTML:**
    7. OBRIGATÓRIO: Use APENAS formatação em negrito com tags <strong> para destacar elementos importantes
    8. OBRIGATÓRIO: NUNCA use sublinhado (<u> tags), itálico (<em> tags), ou qualquer outra formatação
    9. OBRIGATÓRIO: NÃO inclua cabeçalhos como "Características principais:" ou similares
    10. OBRIGATÓRIO: Apresente as características diretamente em parágrafos ou texto corrido
    11. OBRIGATÓRIO: Evite títulos de secções desnecessários nas descrições
    12. OBRIGATÓRIO: Use <strong> inteligentemente para destacar:
        - Especificações técnicas importantes (percentagens, medidas, capacidades)
        - Nomes de tecnologias e processos
        - Benefícios-chave únicos do produto
        - Características distintivas
        - Nomes de marcas ou certificações
    13. OBRIGATÓRIO: Mantenha formatação HTML limpa e compatível com WooCommerce

    Com base nisso, reescreva e otimize o conteúdo, corrigindo automaticamente todos os erros ortográficos e gerando os seguintes elementos em português de Portugal perfeito. A resposta DEVE ser um objeto JSON válido.

    **IMPORTANTE:** A curta descrição deve ser uma versão resumida e específica da descrição principal, não um texto genérico. Extraia as características mais importantes da descrição completa.

    **CRÍTICO - LIMITE SEO:** A descrição SEO (shortDescription) DEVE ter MÁXIMO 160 caracteres. CONTE OS CARACTERES. Se passar de 160, REESCREVA até ficar dentro do limite. Seja específico sobre o produto, não genérico.

    1.  **wooCommerceMainDescription (Descrição WooCommerce):**
        -   **Objetivo:** Ser a descrição completa do produto no WooCommerce. Deve ser rica em detalhes e otimizada para conversão.
        -   **Estilo:** Use formatação HTML estruturada para melhor apresentação visual.
        -   **Tom:** Abrangente, informativo e persuasivo.
        -   **Formato OBRIGATÓRIO:** Use esta estrutura HTML:
            1. Parágrafo de abertura com tag p
            2. Características diretamente em parágrafos separados (sem listas ul/li e SEM títulos como "Características principais:")
            3. Parágrafo de fecho com tag p
        -   **Formatação HTML obrigatória:** Use APENAS <strong> tags para destacar elementos importantes. NUNCA use <u> ou <em>.
        -   **Formatação das características:** Cada característica deve ser um parágrafo separado, claro e específico, destacando um benefício único. Use linguagem persuasiva e técnica quando apropriado.
        -   **IMPORTANTE:** NÃO inclua títulos como "Características principais:" ou similares. Apresente as características diretamente.
        -   **Uso inteligente de <strong>:** Destaque especificações técnicas, nomes de tecnologias, benefícios-chave, certificações e características distintivas.
        -   **Exemplo de estrutura correta:**
            <p>Parágrafo introdutório...</p>
            <p>Este produto oferece <strong>tecnologia avançada</strong> que proporciona <strong>desempenho superior</strong> em todas as condições.</p>
            <p>Com <strong>certificação premium</strong> e <strong>garantia estendida</strong>, garante máxima qualidade e durabilidade.</p>
            <p>Parágrafo de fecho...</p>
        -   **Tamanho:** Entre 200-400 palavras para ser completa mas não excessiva.

    2.  **wooCommerceShortDescription (Curta Descrição WooCommerce):**
        -   **Objetivo:** Ser uma versão resumida e específica da descrição principal, destacando os pontos mais importantes do produto específico.
        -   **Estilo:** Deve ser uma síntese da descrição principal, mantendo as características mais relevantes e específicas do produto em questão.
        -   **Tom:** Direto, específico e focado nos benefícios únicos deste produto em particular.
        -   **Formato:** Use parágrafos separados (sem listas ul/li) com 3-4 características principais extraídas da descrição completa.
        -   **Formatação HTML obrigatória:** Use APENAS <strong> tags para destacar elementos-chave. NUNCA use <u> ou <em>.
        -   **Exemplo de formatação correta:** <p>Este produto oferece <strong>tecnologia avançada</strong> com <strong>desempenho superior</strong> para máxima eficiência.</p><p>Com <strong>certificação premium</strong> garante qualidade e durabilidade excepcionais.</p>
        -   **Importante:** NÃO use frases genéricas. Seja específico sobre este produto em particular.
        -   **Tamanho:** Máximo 3-4 itens, cada um com características específicas do produto destacadas com <strong>.

    3.  **shortDescription (Descrição SEO):**
        -   **Objetivo:** Meta description inteligente e específica que identifique exatamente o que é o produto e seus benefícios únicos.
        -   **REGRA ABSOLUTA:** MÁXIMO 160 caracteres (incluindo espaços). Conte cada caractere. Se passar de 160, REESCREVA até ficar dentro do limite.
        -   **Conteúdo OBRIGATÓRIO:** Deve incluir:
            1. O QUE é o produto (tipo específico, não genérico)
            2. PRINCIPAL característica/benefício único
            3. PARA QUEM é destinado (se relevante)
        -   **Estilo:** Específico, direto, sem palavras vazias como "alta qualidade", "excelente", "premium" (a menos que seja realmente diferencial)
        -   **Exemplos de RUIM (genéricos):**
            * "Produto de alta qualidade com excelente design e materiais premium"
            * "Artigo moderno e funcional para uso diário"
        -   **Exemplos de BOM (específicos):**
            * "Sapatos de couro italiano com palmilha gel anti-impacto para profissionais" (79 chars)
            * "Smartphone Android 12GB RAM, câmara 108MP, bateria 5000mAh" (60 chars)
            * "Mesa escritório madeira maciça 120x60cm com gavetas organizadoras" (67 chars)
        -   **FÓRMULA:** [TIPO PRODUTO] + [MATERIAL/TECNOLOGIA] + [BENEFÍCIO PRINCIPAL] + [PÚBLICO-ALVO se relevante]
        -   **VERIFICAÇÃO FINAL:** Conte os caracteres. Se > 160, corte palavras até ficar ≤ 160.

    4.  **slug (Slug):**
        -   **Objetivo:** Gerar um slug de URL otimizado a partir do nome do produto ou, se não disponível, a partir da descrição.
        -   **Estilo:** Minúsculas, hífens para espaços, sem "stop words".

    Responda APENAS com o objeto JSON, formatado da seguinte maneira:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  try {
    const response = await openai.chat.completions.create({
      model: preferredModel, // Dynamic model selection from environment
      messages: [
        {
          role: "system",
          content: "Você é um especialista avançado em SEO e copywriting para e-commerce português, especializado em reescrever e otimizar conteúdo com MÁXIMA DIVERSIDADE estrutural e estilística. Sua especialidade é corrigir automaticamente erros ortográficos e produzir texto em português de Portugal impecável, usando aberturas dinâmicas e estruturas completamente variadas. CRÍTICO: Cada reescrita deve ser TOTALMENTE ÚNICA em abordagem, vocabulário e estrutura sintáctica. PRESERVE INTEGRALMENTE todo o conteúdo original fornecido pelo utilizador. Analise o tipo de produto antes de escolher aberturas apropriadas. VARIE drasticamente: comprimento de frases, perspectivas narrativas, registos linguísticos e ângulos de abordagem. FORMATAÇÃO OBRIGATÓRIA: Use APENAS formatação em negrito (<strong> tags) para destacar elementos importantes como características técnicas, benefícios-chave, especificações e nomes de marcas. NUNCA use sublinhado (<u> tags), itálico (<em> tags) ou qualquer outra formatação. Sua tarefa é reescrever descrições SEM LIMITES de caracteres para descrições principais, mantendo português europeu perfeito, máxima originalidade e formatação HTML limpa."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.8, // High creativity for maximum diversity
      max_tokens: 3000, // Increased significantly with 1M context window
      response_format: { type: "json_object" },
      top_p: 0.95, // High diversity for lexical variation
      frequency_penalty: 0.6, // Strong repetition prevention
      presence_penalty: 0.4, // Encourages topic and structural diversity
    });

    const content = response.choices[0].message.content;
    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const parsedContent: SeoContent = JSON.parse(content);

      // Debug: Log original content before processing
      console.log("🔍 Original shortDescription (improve):", JSON.stringify(parsedContent.shortDescription));

      // Aplicar limpeza de formatação e correção ortográfica automática a todos os campos de texto
      parsedContent.wooCommerceMainDescription = correctPortugueseText(cleanUnwantedFormatting(parsedContent.wooCommerceMainDescription));
      parsedContent.wooCommerceShortDescription = correctPortugueseText(cleanUnwantedFormatting(parsedContent.wooCommerceShortDescription));
      parsedContent.shortDescription = correctPortugueseText(cleanUnwantedFormatting(parsedContent.shortDescription));

      // Debug: Log processed content
      console.log("🔍 Processed shortDescription (improve):", JSON.stringify(parsedContent.shortDescription));

      // Garante que o slug está bem formatado
      parsedContent.slug = generateSlug(parsedContent.slug || productName || currentDescription.substring(0, 50));

      // VALIDAÇÃO CRÍTICA: Preservação de informações da descrição original
      // Nota: Na função de melhoria, validamos se o conteúdo original foi preservado
      if (currentDescription && currentDescription.trim() !== '') {
        const preservationValidation = validateAdditionalInfoPreservation(parsedContent, currentDescription);

        if (preservationValidation.preservationRate < 70) {
          console.warn("⚠️ AVISO: Baixa preservação do conteúdo original:", preservationValidation.preservationRate.toFixed(1) + "%");
          console.warn("Elementos possivelmente perdidos:", preservationValidation.missingElements);
        } else {
          console.log("✅ Conteúdo original preservado adequadamente:", preservationValidation.preservationRate.toFixed(1) + "%");
        }
      }

      // Validar limites de SEO
      const validation = validateSeoLimits(parsedContent);
      if (!validation.isValid) {
        console.warn("Limites de SEO não respeitados:", validation.errors);

        // Tentar corrigir a meta description se estiver fora dos limites
        if (parsedContent.shortDescription.length < 140) {
          console.log(`🔧 Expandindo meta description: ${parsedContent.shortDescription.length} -> ~150 caracteres`);
          // Se muito curta, expandir ligeiramente
          parsedContent.shortDescription = parsedContent.shortDescription + " Descubra mais detalhes.";
        } else if (parsedContent.shortDescription.length > 160) {
          console.log(`🔧 Truncando meta description (improve): ${parsedContent.shortDescription.length} -> 160 caracteres`);

          // Se muito longa, truncar de forma inteligente
          let truncated = parsedContent.shortDescription.substring(0, 157);

          // Tentar cortar na última palavra completa
          const lastSpace = truncated.lastIndexOf(' ');
          if (lastSpace > 140) {
            truncated = truncated.substring(0, lastSpace);
          }

          // Limpar caracteres especiais no final antes de adicionar reticências
          truncated = truncated.replace(/[.,;:!?\s]+$/, '');

          parsedContent.shortDescription = truncated + "...";
          console.log(`✅ Meta description corrigida (improve): ${parsedContent.shortDescription.length} caracteres`);
          console.log(`📝 Resultado: "${parsedContent.shortDescription}"`);
        }
      }

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Re-throw OpenAI API errors to be handled by the main catch block
    throw error;
  }
}
